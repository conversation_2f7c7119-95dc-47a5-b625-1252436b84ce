import bycrypt from "bcryptjs";
import User from "../models/user.model.js";

export const signup = async (req, res) => {
  const { fullname, email, password } = req.body;
  console.log(req.body);
  try {

    if(password<6){
      return res.status(400).json({message: "Password must be at least 6 characters"});
    }

    const user = await User.findOne({email});
    if(user){
      return res.status(400).json({message: "User already exists"});
    }
    //hash password
    const salt = bycrypt.genSaltSync(10);
    const hashedPassword = bycrypt.hashSync(password, salt);

    const newUser = new User({fullname, email, password: hashedPassword});

    if(newUser){
      generateToken(newUser, res);
      await newUser.save();
      return res.status(201).json({
        _id:newUser._id,
        fullname: newUser.fullname,
        email: newUser.email,
        profilepic: newUser.profilepic,
      });
    }else{
      return res.status(400).json({message: "Something went wrong"});
    }

  } catch (error) {
    res.status(500).json({message: "Internal Sever error"}); 
  }
};

export const login = (req, res) => {
  res.send("login");
};

export const logout = (req, res) => {
  res.send("logout");
};
